import 'package:flutter/material.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import '../../../widgets/common/login/login_screen_factory.dart';

/// B2C-specific login screen
/// Uses the common login widget with B2C-specific navigation
class LoginScreenB2C extends StatelessWidget {
  final List<UserActivitySetting> settings;

  const LoginScreenB2C({
    Key? key,
    required this.settings,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LoginScreenFactory.createLoginScreen(
      settings: settings,
      context: context,
    );
  }
}
