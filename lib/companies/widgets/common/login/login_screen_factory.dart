import 'package:flutter/material.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/branding/branding.dart';
import 'common_login_screen.dart';

// Company-specific OTP screen imports
import '../../../b2c/views/otp/otp_verification_screen_b2c.dart';
import '../../../lapa/views/otp/otp_verification_screen_lapa.dart';
import '../../../nds/views/otp/otp_verification_screen_nds.dart';
import '../../../prodred/views/otp/otp_verification_screen_prodred.dart';
import '../../../nichesolv/views/otp/otp_verification_screen_nichesolv.dart';

class LoginScreenFactory {
  static Widget createLoginScreen({
    required List<UserActivitySetting> settings,
    required BuildContext context,
  }) {
    return CommonLoginScreen(
      settings: settings,
      screenName: _getScreenName(),
      onNavigateToOtp: (phoneNumber, phoneNumText, settings) {
        _navigateToOtpScreen(context, phoneNumber, phoneNumText, settings);
      },
    );
  }

  static String _getScreenName() {
    switch (companyName) {
      case 'b2c':
        return 'B2C Login Screen';
      case 'lapa':
        return 'Lapa Login Screen';
      case 'nds':
        return 'NDS Login Screen';
      case 'prodred':
        return 'ProdRed Login Screen';
      case 'Nichesolv':
        return 'Nichesolv Login Screen';
      default:
        return 'Login Screen';
    }
  }

  static void _navigateToOtpScreen(
    BuildContext context,
    String phoneNumber,
    String phoneNumText,
    List<UserActivitySetting> settings,
  ) {
    Widget otpScreen;

    switch (companyName) {
      case 'b2c':
        otpScreen = OtpVerificationScreenB2C(
          phoneNumber: phoneNumber,
          phoneNumText: phoneNumText,
          settings: settings,
        );
        break;
      case 'lapa':
        otpScreen = OtpVerificationScreenLapa(
          phoneNumber: phoneNumber,
          phoneNumText: phoneNumText,
          settings: settings,
        );
        break;
      case 'nds':
        otpScreen = OtpVerificationScreenNDS(
          phoneNumber: phoneNumber,
          phoneNumText: phoneNumText,
          settings: settings,
        );
        break;
      case 'prodred':
        otpScreen = OtpVerificationScreenProdRed(
          phoneNumber: phoneNumber,
          phoneNumText: phoneNumText,
          settings: settings,
        );
        break;
      case 'Nichesolv':
        otpScreen = OtpVerificationScreenNichesolv(
          phoneNumber: phoneNumber,
          phoneNumText: phoneNumText,
          settings: settings,
        );
        break;
      default:
        otpScreen = OtpVerificationScreenNDS(
          phoneNumber: phoneNumber,
          phoneNumText: phoneNumText,
          settings: settings,
        );
        break;
    }

    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => otpScreen),
    );
  }

  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'Nichesolv'];
  }

  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
