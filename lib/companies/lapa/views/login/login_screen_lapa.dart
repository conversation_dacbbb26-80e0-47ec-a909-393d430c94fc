import 'package:flutter/material.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import '../../../widgets/common/login/login_screen_factory.dart';

/// Lapa-specific login screen
/// Uses the common login widget with Lapa-specific navigation
class LoginScreenLapa extends StatelessWidget {
  final List<UserActivitySetting> settings;

  const LoginScreenLapa({
    Key? key,
    required this.settings,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LoginScreenFactory.createLoginScreen(
      settings: settings,
      context: context,
    );
  }
}
